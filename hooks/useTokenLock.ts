import { CONTRACT_ADDRESSES, ERC20_ABI, TOKEN_LOCK_ABI } from '@/constants';
import { errorMsg, successMsg } from '@/libs/toast';
import { extractUserFriendlyError } from '@/utils/errorHandling';
import BigNumber from 'bignumber.js';
import { Contract, formatUnits, parseUnits } from 'ethers';
import { useCallback, useState } from 'react';
import { useSigner } from './useSigner';

// TypeScript interfaces for TokenLock data structures
export interface LockInfo {
  token: string;
  amount: string;
  startTime: string;
  endTime: string;
  recipient: string;
  locker: string;
  closed: boolean;
}

export interface CreateLockParams {
  tokenAddress: string;
  amount: string;
  lockDurationSeconds: number;
  recipient?: string; // Optional, defaults to current user
}

interface TokenLockResult {
  // State
  isLoading: boolean;
  isCreatingLock: boolean;
  isWithdrawing: boolean;
  isFetchingLock: boolean;
  error: string | null;
  lockInfo: LockInfo | null;
  lastCreatedLockId: string | null;

  // Functions
  createLock: (params: CreateLockParams) => Promise<string | null>;
  withdrawLock: (lockId: string) => Promise<void>;
  getLockInfo: (lockId: string) => Promise<LockInfo | null>;
  clearError: () => void;
  clearLockInfo: () => void;
}

export const useTokenLock = (): TokenLockResult => {
  const [isLoading, setIsLoading] = useState(false);
  const [isCreatingLock, setIsCreatingLock] = useState(false);
  const [isWithdrawing, setIsWithdrawing] = useState(false);
  const [isFetchingLock, setIsFetchingLock] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lockInfo, setLockInfo] = useState<LockInfo | null>(null);
  const [lastCreatedLockId, setLastCreatedLockId] = useState<string | null>(
    null,
  );

  const { signer } = useSigner();

  const createLock = useCallback(
    async (params: CreateLockParams): Promise<string | null> => {
      if (!signer || !signer.address) {
        errorMsg('Please connect your wallet');
        return null;
      }

      const { tokenAddress, amount, lockDurationSeconds, recipient } = params;

      if (!tokenAddress || !amount || lockDurationSeconds <= 0) {
        errorMsg(
          'Please provide valid token address, amount, and lock duration',
        );
        return null;
      }

      if (new BigNumber(amount).isLessThanOrEqualTo(0)) {
        errorMsg('Amount must be greater than 0');
        return null;
      }

      setIsCreatingLock(true);
      setError(null);

      try {
        const tokenLockContract = new Contract(
          CONTRACT_ADDRESSES.TOKEN_LOCK,
          TOKEN_LOCK_ABI,
          signer,
        );

        const tokenContract = new Contract(tokenAddress, ERC20_ABI, signer);

        const decimals = await tokenContract.decimals();
        const tokenSymbol = await tokenContract.symbol();

        const amountInWei = parseUnits(amount, decimals);

        const userBalance = await tokenContract.balanceOf(signer.address);

        if (userBalance < amountInWei) {
          throw new Error(
            `Insufficient token balance. You have ${formatUnits(
              userBalance,
              decimals,
            )} ${tokenSymbol}, but need ${amount} ${tokenSymbol}`,
          );
        }

        const currentAllowance = await tokenContract.allowance(
          signer.address,
          CONTRACT_ADDRESSES.TOKEN_LOCK,
        );
        console.log(
          'Current allowance:',
          formatUnits(currentAllowance, decimals),
          tokenSymbol,
        );

        if (currentAllowance < amountInWei) {
          const approveTx = await tokenContract.approve(
            CONTRACT_ADDRESSES.TOKEN_LOCK,
            amountInWei,
          );
          console.log('Approval transaction hash:', approveTx.hash);

          await approveTx.wait();
          successMsg('Token approval confirmed');
        }

        const currentTime = Math.floor(Date.now() / 1000);
        const endTime = currentTime + params.lockDurationSeconds;

        const lockRecipient = recipient || signer.address;

        const tx = await tokenLockContract.createLock(
          tokenAddress,
          amountInWei,
          endTime,
          lockRecipient,
        );

        console.log('Create lock transaction hash:', tx.hash);

        const receipt = await tx.wait();

        let lockId = null;
        if (receipt.logs && receipt.logs.length > 0) {
          try {
            for (const log of receipt.logs) {
              try {
                const parsedLog = tokenLockContract.interface.parseLog({
                  topics: log.topics,
                  data: log.data,
                });

                if (parsedLog && parsedLog.name === 'LockCreated') {
                  lockId = parsedLog.args.lockId.toString();
                  console.log(
                    'Lock ID extracted from LockCreated event:',
                    lockId,
                  );
                  break;
                }
              } catch (parseError) {
                continue;
              }
            }
          } catch (eventError) {
            console.warn('Could not parse lock ID from events:', eventError);
          }
        }

        successMsg('Token lock created successfully!');
        setLastCreatedLockId(lockId);

        return lockId;
      } catch (error: any) {
        console.error('Create lock error:', error);
        const errorMessage = extractUserFriendlyError(
          error,
          'Failed to create token lock',
        );
        setError(errorMessage);
        errorMsg(errorMessage);
        return null;
      } finally {
        setIsCreatingLock(false);
      }
    },
    [signer],
  );

  const withdrawLock = useCallback(
    async (lockId: string): Promise<void> => {
      if (!signer || !signer.address) {
        errorMsg('Please connect your wallet first');
        return;
      }

      if (!lockId || lockId === '0') {
        errorMsg('Please provide a valid lock ID');
        return;
      }

      setIsWithdrawing(true);
      setError(null);

      try {
        const tokenLockContract = new Contract(
          CONTRACT_ADDRESSES.TOKEN_LOCK,
          TOKEN_LOCK_ABI,
          signer,
        );

        const lockData = await tokenLockContract.getLock(lockId);

        if (!lockData) {
          throw new Error(`Lock with ID ${lockId} not found`);
        }

        if (lockData.closed) {
          throw new Error('This lock has already been withdrawn');
        }

        const userAddress = signer.address.toLowerCase();
        const isRecipient = lockData.recipient.toLowerCase() === userAddress;
        const isLocker = lockData.locker.toLowerCase() === userAddress;

        let isAdmin = false;
        try {
          const config = await tokenLockContract.getConfig();
          isAdmin = config.toLowerCase() === userAddress;
        } catch (adminError) {
          console.warn('Could not fetch admin config:', adminError);
        }

        if (!isRecipient && !isLocker && !isAdmin) {
          throw new Error(
            'You are not authorized to withdraw from this lock. Only the recipient, locker, or admin can withdraw.',
          );
        }

        const currentTime = Math.floor(Date.now() / 1000);
        const endTime = parseInt(lockData.endTime.toString());

        if (currentTime < endTime) {
          const remainingTime = endTime - currentTime;
          const remainingDays = Math.ceil(remainingTime / (24 * 60 * 60));
          throw new Error(
            `Lock period has not expired yet. Please wait ${remainingDays} more day(s).`,
          );
        }

        const isWithdrawable = await tokenLockContract.isWithdrawable(lockId);
        if (!isWithdrawable) {
          throw new Error('Lock is not withdrawable at this time');
        }

        const tx = await tokenLockContract.withdraw(lockId);

        successMsg('Withdrawal transaction submitted successfully');
        console.log('Withdrawal transaction details:', {
          hash: tx.hash,
          lockId,
        });

        const receipt = await tx.wait();

        if (receipt.status === 1) {
          setLockInfo(null);

          console.log('Withdrawal completed:', {
            transactionHash: receipt.hash,
            blockNumber: receipt.blockNumber,
            gasUsed: receipt.gasUsed?.toString(),
            lockId,
          });
        } else {
          throw new Error('Transaction failed during execution');
        }
      } catch (error: any) {
        console.error('Withdrawal error:', error);
        const errorMessage = extractUserFriendlyError(
          error,
          'Failed to withdraw tokens',
        );

        setError(errorMessage);
        errorMsg(errorMessage);
      } finally {
        setIsWithdrawing(false);
      }
    },
    [signer],
  );

  const getLockInfo = useCallback(
    async (lockId: string): Promise<LockInfo | null> => {
      if (!signer || !lockId) {
        return null;
      }

      setIsFetchingLock(true);
      setError(null);

      try {
        const tokenLockContract = new Contract(
          CONTRACT_ADDRESSES.TOKEN_LOCK,
          TOKEN_LOCK_ABI,
          signer,
        );

        const lockData = await tokenLockContract.getLock(lockId);

        if (!lockData) {
          throw new Error('Lock not found');
        }

        const tokenContract = new Contract(lockData.token, ERC20_ABI, signer);

        const decimals = await tokenContract.decimals();

        const lockInfo: LockInfo = {
          token: lockData.token,
          amount: formatUnits(lockData.amount, decimals),
          startTime: lockData.startTime.toString(),
          endTime: lockData.endTime.toString(),
          recipient: lockData.recipient,
          locker: lockData.locker,
          closed: lockData.closed,
        };

        console.log('Lock info fetched:', {
          lockId,
          lockInfo,
          endTimeDate: new Date(
            parseInt(lockInfo.endTime) * 1000,
          ).toISOString(),
        });

        setLockInfo(lockInfo);
        return lockInfo;
      } catch (error: any) {
        console.error('Get lock info error:', error);
        const errorMessage =
          error?.message || 'Failed to fetch lock information';
        setError(errorMessage);
        setLockInfo(null);
        return null;
      } finally {
        setIsFetchingLock(false);
      }
    },
    [signer],
  );

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const clearLockInfo = useCallback(() => {
    setLockInfo(null);
  }, []);

  return {
    isLoading,
    isCreatingLock,
    isWithdrawing,
    isFetchingLock,
    error,
    lockInfo,
    lastCreatedLockId,
    createLock,
    withdrawLock,
    getLockInfo,
    clearError,
    clearLockInfo,
  };
};
