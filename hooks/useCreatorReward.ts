import { useState, useCallback, useEffect } from 'react';
import { Contract, formatEther } from 'ethers';
import { CONTRACT_ADDRESSES, LAUNCHPAD_ABI } from '@/constants';
import { useSigner } from '@/hooks/useSigner';
import { useCoinPageContext } from '@/app/coins/[slug]/provider';
import { errorMsg, successMsg } from '@/libs/toast';
import { getContractErrorMessage } from '@/utils/errorHandling';

interface CreatorRewardInfo {
  creator: string;
  unclaimedReward: string;
}

export const useCreatorReward = () => {
  const { signer } = useSigner();
  const { coinAddress, coin } = useCoinPageContext();
  const [creatorRewardInfo, setCreatorRewardInfo] =
    useState<CreatorRewardInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isClaiming, setIsClaiming] = useState(false);
  const [isEligibleCreator, setIsEligibleCreator] = useState(false);
  const getCreatorRewardInfo = useCallback(async () => {
    if (!signer || !coinAddress || !coin?.creatorAddress) return null;

    try {
      setIsLoading(true);
      const launchpadContract = new Contract(
        CONTRACT_ADDRESSES.LAUNCHPAD,
        LAUNCHPAD_ABI,
        signer,
      );
      console.log(coinAddress, 'coinAddress');

      // Get unclaimed reward amount from Launchpad contract
      const unclaimedReward = await launchpadContract.getUnclaimedCreatorReward(
        coinAddress,
      );
      console.log(unclaimedReward, 'unclaimedReward from Launchpad');

      const formattedInfo: CreatorRewardInfo = {
        creator: coin.creatorAddress, // Use creator address from API data
        unclaimedReward: formatEther(unclaimedReward),
      };
      console.log(formattedInfo, 'formattedInfo');

      setCreatorRewardInfo(formattedInfo);

      const userAddress = await signer.getAddress();
      setIsEligibleCreator(
        formattedInfo.creator.toLowerCase() === userAddress.toLowerCase(),
      );

      return formattedInfo;
    } catch (error: any) {
      console.error(
        'Error fetching creator reward info from Launchpad:',
        error,
      );

      if (error?.reason === 'CreatorNotExist') {
        setCreatorRewardInfo(null);
        setIsEligibleCreator(false);
        return null;
      }

      errorMsg('Failed to fetch creator reward information');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [signer, coinAddress, coin?.creatorAddress]);
  const claimCreatorPool = useCallback(async () => {
    if (!signer || !coinAddress || !isEligibleCreator) {
      errorMsg('You are not eligible to claim rewards for this token');
      return false;
    }

    try {
      setIsClaiming(true);
      const launchpadContract = new Contract(
        CONTRACT_ADDRESSES.LAUNCHPAD,
        LAUNCHPAD_ABI,
        signer,
      );

      // Check pool status to determine which function to use
      const poolInfo = await launchpadContract.getPool(coinAddress);

      let tx;
      if (poolInfo.isCompleted) {
        // For completed pools (bonded tokens), use collectHyperSwapFees
        tx = await launchpadContract.collectHyperSwapFees(coinAddress);
      } else {
        // For incomplete pools (not yet bonded), use distributeBondingCurveFees
        tx = await launchpadContract.distributeBondingCurveFees(coinAddress);
      }

      await tx.wait();

      successMsg('Creator rewards claimed successfully!');

      await getCreatorRewardInfo();

      return true;
    } catch (error: any) {
      console.error('Error claiming creator rewards:', error);

      const userFriendlyMessage = getContractErrorMessage(
        error,
        'Failed to claim creator rewards',
      );
      errorMsg(userFriendlyMessage);

      return false;
    } finally {
      setIsClaiming(false);
    }
  }, [signer, coinAddress, isEligibleCreator, getCreatorRewardInfo]);
  const checkCreatorPoolExists = useCallback(async () => {
    if (!signer || !coinAddress) return false;

    try {
      // Use Launchpad contract to check if token exists
      const launchpadContract = new Contract(
        CONTRACT_ADDRESSES.LAUNCHPAD,
        LAUNCHPAD_ABI,
        signer,
      );

      return await launchpadContract.isToken(coinAddress);
    } catch (error) {
      console.error('Error checking creator pool existence:', error);
      return false;
    }
  }, [signer, coinAddress]);

  useEffect(() => {
    if (signer && coinAddress) {
      getCreatorRewardInfo();
    }
  }, [signer, coinAddress, getCreatorRewardInfo]);

  // getCreatorRewardInfo is already defined above, we'll use it directly

  return {
    creatorRewardInfo,
    isLoading,
    isClaiming,
    isEligibleCreator,
    getCreatorRewardInfo,
    claimCreatorPool,
    checkCreatorPoolExists,
    hasRewards:
      creatorRewardInfo && parseFloat(creatorRewardInfo.unclaimedReward) > 0,
    rewardAmount: creatorRewardInfo?.unclaimedReward || '0',
    // Backward compatibility - keep the old interface for existing components
    creatorPoolInfo: creatorRewardInfo
      ? {
          creator: creatorRewardInfo.creator,
          totalRewards: creatorRewardInfo.unclaimedReward,
          initializer: '', // Not available from getUnclaimedCreatorReward, but not used in components
        }
      : null,
    getCreatorPoolInfo: getCreatorRewardInfo, // Alias for backward compatibility
  };
};
