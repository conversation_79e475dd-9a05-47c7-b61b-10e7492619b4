/**
 * Utility functions for handling transaction errors and extracting user-friendly messages
 */

interface EthersError {
  code?: string | number;
  reason?: string;
  message?: string;
  info?: {
    error?: {
      code?: number;
      message?: string;
      data?: any;
    };
  };
  action?: string;
  shortMessage?: string;
}

/**
 * Extracts a user-friendly error message from various error formats
 * @param error - The error object from ethers or wallet interactions
 * @param fallbackMessage - Default message if no user-friendly message can be extracted
 * @returns A clean, user-friendly error message
 */
export function extractUserFriendlyError(
  error: any,
  fallbackMessage: string = 'Transaction failed'
): string {
  // Handle null/undefined errors
  if (!error) {
    return fallbackMessage;
  }

  // Check for user rejection (code 4001 or ACTION_REJECTED)
  if (isUserRejection(error)) {
    return 'Transaction was cancelled by user';
  }

  // Try to extract message from various error structures
  const errorMessage = getErrorMessage(error);
  
  // Clean up the message to remove technical details
  return cleanErrorMessage(errorMessage) || fallbackMessage;
}

/**
 * Checks if the error is a user rejection
 * @param error - The error object
 * @returns true if the error represents user rejection
 */
export function isUserRejection(error: any): boolean {
  if (!error) return false;

  // Check for ethers ACTION_REJECTED
  if (error.code === 'ACTION_REJECTED') {
    return true;
  }

  // Check for MetaMask user rejection (code 4001)
  if (error.code === 4001) {
    return true;
  }

  // Check nested error structures
  if (error.info?.error?.code === 4001) {
    return true;
  }

  // Check for user denial in message
  const message = getErrorMessage(error).toLowerCase();
  if (
    message.includes('user denied') ||
    message.includes('user rejected') ||
    message.includes('user cancelled') ||
    message.includes('transaction signature') ||
    message.includes('user canceled')
  ) {
    return true;
  }

  return false;
}

/**
 * Extracts the error message from various error object structures
 * @param error - The error object
 * @returns The extracted error message
 */
function getErrorMessage(error: any): string {
  if (typeof error === 'string') {
    return error;
  }

  // Try different message properties in order of preference
  const messagePaths = [
    'shortMessage',
    'reason',
    'message',
    'info.error.message',
  ];

  for (const path of messagePaths) {
    const message = getNestedProperty(error, path);
    if (message && typeof message === 'string') {
      return message;
    }
  }

  return '';
}

/**
 * Gets a nested property from an object using dot notation
 * @param obj - The object to search
 * @param path - The dot-separated path (e.g., 'info.error.message')
 * @returns The value at the path or undefined
 */
function getNestedProperty(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

/**
 * Cleans up error messages to remove technical details and make them user-friendly
 * @param message - The raw error message
 * @returns A cleaned, user-friendly message
 */
function cleanErrorMessage(message: string): string {
  if (!message) return '';

  // Remove technical prefixes and suffixes
  let cleaned = message
    .replace(/^action="[^"]*",?\s*/i, '') // Remove action="sendTransaction"
    .replace(/^reason="[^"]*",?\s*/i, '') // Remove reason="rejected"
    .replace(/,?\s*info=\{.*\}$/i, '') // Remove info={...} at the end
    .replace(/,?\s*code=[A-Z_]+$/i, '') // Remove code=ACTION_REJECTED at the end
    .replace(/,?\s*version=[\d.]+$/i, '') // Remove version=6.14.4 at the end
    .replace(/^ethers-user-denied:\s*/i, '') // Remove ethers-user-denied: prefix
    .replace(/^MetaMask Tx Signature:\s*/i, '') // Remove MetaMask Tx Signature: prefix
    .trim();

  // Extract user-friendly messages from common patterns
  const userFriendlyPatterns = [
    /User denied transaction signature\.?/i,
    /User rejected the request\.?/i,
    /User cancelled the transaction\.?/i,
    /Transaction was rejected\.?/i,
    /Insufficient funds/i,
    /Gas estimation failed/i,
    /Network error/i,
    /Invalid token/i,
    /Token not found/i,
    /Allowance exceeded/i,
    /Slippage tolerance exceeded/i,
  ];

  for (const pattern of userFriendlyPatterns) {
    const match = cleaned.match(pattern);
    if (match) {
      return match[0];
    }
  }

  // If the cleaned message is still too technical or empty, return empty
  // This will cause the fallback message to be used
  if (
    !cleaned ||
    cleaned.length > 200 ||
    cleaned.includes('0x') ||
    cleaned.includes('jsonrpc') ||
    cleaned.includes('payload')
  ) {
    return '';
  }

  return cleaned;
}

/**
 * Common error messages for specific contract errors
 */
export const CONTRACT_ERROR_MESSAGES: Record<string, string> = {
  // Creator reward errors
  'RewardToClaimNotValid': 'No rewards available to claim',
  'CreatorNotExist': 'Creator pool does not exist for this token',
  'InvalidCreator': 'You are not the creator of this token',
  
  // General contract errors
  'InsufficientBalance': 'Insufficient balance',
  'TransferFailed': 'Transfer failed',
  'ApprovalFailed': 'Token approval failed',
  'SlippageExceeded': 'Price slippage exceeded tolerance',
  'DeadlineExceeded': 'Transaction deadline exceeded',
  'TokenNotExists': 'Token does not exist',
  'Unauthorized': 'You are not authorized to perform this action',
  
  // Network errors
  'NETWORK_ERROR': 'Network error. Please check your connection',
  'TIMEOUT': 'Transaction timed out. Please try again',
  'NONCE_EXPIRED': 'Transaction nonce expired. Please try again',
};

/**
 * Gets a user-friendly message for contract-specific errors
 * @param error - The error object
 * @param fallbackMessage - Default message if no specific message is found
 * @returns A user-friendly error message
 */
export function getContractErrorMessage(
  error: any,
  fallbackMessage: string = 'Transaction failed'
): string {
  if (!error) return fallbackMessage;

  // Check for user rejection first
  if (isUserRejection(error)) {
    return 'Transaction was cancelled by user';
  }

  // Check for specific contract error reasons
  if (error.reason && CONTRACT_ERROR_MESSAGES[error.reason]) {
    return CONTRACT_ERROR_MESSAGES[error.reason];
  }

  // Fall back to general error extraction
  return extractUserFriendlyError(error, fallbackMessage);
}
